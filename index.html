<!DOCTYPE html>
<html lang="zh" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Embodied-R</title>
    <link rel="icon" href="source/Embodied-R.png" type="image/png">
    <link rel="stylesheet" href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/jpswalsh/academicons@1/css/academicons.min.css">
    <style>
        :root {
            --primary-color: #3182ce;
            --primary-color-rgb: 49, 130, 206;
            --secondary-color: #2c5282;
            --accent-color: #4299e1;
            --text-color: #2d3748;
            --bg-color: #f7fafc;
            --bg-color-rgb: 247, 250, 252;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
            --highlight-bg: #fef9f9;
            --highlight-border: #fadbd8;
            --shadow: 0 4px 15px rgba(0,0,0,0.05);
            --transition: all 0.3s ease;
            --gradient-start: #9f7aea; /* 浅紫色 */
            --gradient-end: #63b3ed; /* 浅蓝色 */
            --primary-dark: #aac1ee; /* 浅蓝色 */
            --secondary-dark: #f6c2ec; /* 浅粉色 */
        }

        .dark {
            --primary-color: #63b3ed;
            --primary-color-rgb: 99, 179, 237;
            --secondary-color: #90cdf4;
            --accent-color: #4299e1;
            --text-color: #e2e8f0;
            --bg-color: #1a202c;
            --bg-color-rgb: 26, 32, 44;
            --card-bg: #2d3748;
            --border-color: #4a5568;
            --highlight-bg: #3c2a2a;
            --highlight-border: #742a2a;
            --shadow: 0 4px 15px rgba(0,0,0,0.3);
            --gradient-start: #b794f4; /* 浅紫色 */
            --gradient-end: #90cdf4; /* 浅蓝色 */
            --primary-dark: #aac1ee; /* 浅蓝色 */
            --secondary-dark: #f6c2ec; /* 浅粉色 */
        }

        /* 阅读进度条样式 */
        .reading-progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            width: 0%;
            z-index: 9999;
            transition: width 0.1s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
            line-height: 1.8;
            color: var(--text-color);
            background-color: var(--bg-color);
            transition: var(--transition);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Noto Serif SC', serif, 'SimHei', 'Microsoft YaHei', '黑体', '宋体';
            font-weight: 600;
            line-height: 1.4;
            color: #1a202c;
            transition: var(--transition);
        }

        .content-container {
            background-color: var(--card-bg);
            border-radius: 1rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            max-width: 1300px;
            margin: 2rem auto;
            padding: 2.5rem;
        }

        .main-title {
            font-size: 2.5rem;
            text-align: center !important;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 3px solid var(--border-color);
            color: #1a202c;
            transition: var(--transition);
            line-height: 1.3;
        }

        .section-title {
            font-size: 2rem;
            color: #1a202c;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 0.75rem;
            margin-top: 3rem;
            margin-bottom: 1.5rem;
            transition: var(--transition);
            text-align: center !important;
        }

        .text-center {
            text-align: center !important;
        }

        .subsection-title {
            font-size: 1.5rem;
            color: #1a202c;
            margin-top: 2rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        p {
            margin: 1rem 0;
            font-size: 1.125rem;
            text-align: justify;
            color: var(--text-color);
            transition: var(--transition);
        }

        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 2rem auto;
            border-radius: 0.5rem;
            box-shadow: var(--shadow);
            transition: transform 0.3s ease;
        }

        img:hover {
            transform: scale(1.01);
        }

        /* 新的封面头部样式 */
        .cover-header {
            position: relative;
            overflow: hidden;
        }

        .cover-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: none;
            background-color: rgb(255, 255, 255);
            z-index: 0;
        }

        .cover-header .container {
            position: relative;
            z-index: 1;
        }

        .text-gradient {
            background: linear-gradient(to right, var(--gradient-start), var(--gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .feature-card {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 0.75rem;
            padding: 0.9rem 1.1rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 0.6rem;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            background-color: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .icon-wrapper {
            width: 42px;
            height: 42px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
            flex-shrink: 0;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-content {
            flex: 1;
        }

        .feature-content h3 {
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
            line-height: 1.3;
        }

        .feature-content p {
            margin: 0;
            font-size: 0.9rem;
            line-height: 1.4;
            color: rgba(255, 255, 255, 0.8);
        }

        /* 添加一些工具类 */
        .container {
            width: 100%;
            margin-left: auto;
            margin-right: auto;
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }

        .max-w-7xl {
            max-width: 80rem;
        }

        .transform {
            transform: translateZ(0);
        }

        .hover\:scale-102:hover {
            transform: scale(1.02);
        }

        .transition-all {
            transition-property: all;
        }

        .duration-500 {
            transition-duration: 500ms;
        }

        .shadow-2xl {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .rounded-xl {
            border-radius: 0.75rem;
        }

        .bg-gradient-to-b {
            background-image: linear-gradient(to bottom, var(--primary-dark), var(--secondary-dark));
        }

        /* 波浪分隔线样式 */
        .wave-divider {
            position: relative;
            height: 120px;
            margin-top: -2px;
            margin-bottom: -2px;
            overflow: hidden;
        }

        .wave-divider svg {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
        }

        .wave-path {
            fill: var(--secondary-dark);
            transition: fill 0.3s ease;
        }

        .dark .wave-path {
            fill: var(--secondary-dark);
        }

        .video-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .video-wrapper {
            background-color: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }

        .video-wrapper:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .video-aspect-wrapper {
            position: relative;
            padding-bottom: 56.25%;
            height: 0;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }

        video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 0.5rem;
        }

        .task-box {
            display: flex;
            flex-direction: column;
            height: 100%;
            transition: var(--transition);
        }

        .task-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .video-question-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .question-section {
            margin-top: 1rem;
            flex-grow: 1;
            transition: var(--transition);
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: rgba(var(--bg-color-rgb, 247, 250, 252), 0.5);
        }

        .question-section:hover {
            background-color: rgba(var(--bg-color-rgb, 247, 250, 252), 0.8);
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }

        .question-section ul li {
            transition: var(--transition);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
        }

        .question-section ul li:hover {
            background-color: rgba(var(--primary-color-rgb, 49, 130, 206), 0.1);
            transform: translateX(5px);
        }

        .question-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .question-box {
            background-color: var(--card-bg);
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }

        .question-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .question-box h3 {
            color: #1a202c;
            margin-top: 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.75rem;
            font-size: 1.5rem;
            transition: var(--transition);
            text-align: center !important;
        }

        .question-box p {
            font-size: 1.1rem;
            text-align: left;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .question-box ul {
            list-style-type: none;
            padding-left: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .question-box li {
            margin-bottom: 0.75rem;
            font-size: 1.05rem;
            padding: 0.5rem;
            border-radius: 0.25rem;
            transition: background-color 0.2s ease;
        }

        .question-box li:hover {
            background-color: rgba(66, 153, 225, 0.1);
        }

        .method-section img,
        .experiment-section img,
        .further-exploration-section img {
            max-width: 85%;
            margin: 2.5rem auto;
        }

        .theme-toggle,
        .back-to-top {
            position: fixed;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
            border: none;
            transition: var(--transition);
        }

        .theme-toggle {
            bottom: 2rem;
            left: 2rem;
        }

        .back-to-top {
            bottom: 2rem;
            right: 2rem;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .theme-toggle:hover,
        .back-to-top:hover {
            background-color: var(--secondary-color);
            transform: translateY(-3px);
        }

        .dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6,
        .dark .main-title, .dark .section-title, .dark .subsection-title,
        .dark .question-box h3 {
            color: #f7fafc;
        }

        /* 封面样式在暗色模式下保持一致 */

        /* 封面特性卡片样式在暗色模式下保持一致 */

        /* 导航栏样式 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background-color: var(--card-bg);
            box-shadow: var(--shadow);
            transition: transform 0.3s ease, background-color 0.3s ease;
            padding: 0.75rem 0;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.9);
            transform: translateY(-100%);
        }

        .navbar.visible {
            transform: translateY(0);
        }

        .dark .navbar {
            background-color: rgba(26, 32, 44, 0.9);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-weight: 700;
            font-size: 1.5rem;
            color: #1a202c;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: var(--transition);
        }

        .dark .logo {
            color: #f7fafc;
        }

        .logo-img {
            height: 40px;
            margin: 0 0.5rem 0 0;
            display: inline-block;
            vertical-align: middle;
            box-shadow: none;
            border-radius: 0;
            transition: transform 0.3s ease;
        }

        .logo-img:hover {
            transform: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-links li {
            margin-left: 2rem;
        }

        .nav-links a {
            color: #1a202c;
            text-decoration: none;
            font-weight: 500;
            position: relative;
            padding: 0.5rem 0;
            transition: var(--transition);
        }

        .dark .nav-links a {
            color: #f7fafc;
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .nav-links a:hover:after {
            width: 100%;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: #1a202c;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .dark .mobile-menu-btn {
            color: #f7fafc;
        }

        /* 动效样式 */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .stagger-item {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .stagger-item.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .highlight {
            color: #e53e3e;
            font-weight: bold;
            text-align: center;
            padding: 1.5rem;
            background-color: var(--highlight-bg);
            border: 1px solid var(--highlight-border);
            border-radius: 0.5rem;
            margin: 2.5rem 0;
            transition: var(--transition);
        }

        strong {
            color: var(--secondary-color);
            font-weight: 600;
            transition: var(--transition);
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
                flex-direction: column;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: var(--card-bg);
                padding: 1rem 0;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            .nav-links.active {
                display: flex;
            }

            .nav-links li {
                margin: 0.5rem 2rem;
            }

            .mobile-menu-btn {
                display: block;
            }

            .content-container {
                padding: 1.5rem;
                margin: 1rem;
            }

            .main-title {
                font-size: 1.8rem;
            }

            .section-title {
                font-size: 1.6rem;
            }

            .subsection-title {
                font-size: 1.3rem;
            }

            p {
                font-size: 1rem;
            }

            .video-container,
            .question-container {
                grid-template-columns: 1fr;
            }

            .theme-toggle,
            .back-to-top {
                width: 2.5rem;
                height: 2.5rem;
            }

            .cover-header .flex {
                flex-direction: column;
            }

            .cover-header .grid {
                grid-template-columns: 1fr;
            }

            .text-gradient {
                font-size: 1.8rem;
            }

            .feature-card {
                padding: 1rem;
            }

            .icon-wrapper {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }
        }

        .affiliation-badge {
            background-color: var(--bg-color);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: inline-block;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-color);
            transition: var(--transition);
        }

        .dark .affiliation-badge {
            background-color: var(--card-bg);
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <!-- 阅读进度条 -->
    <div class="reading-progress-bar" id="reading-progress"></div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">
                <img src="source/Embodied-R.png" alt="Embodied-R Logo" class="logo-img">Embodied-R
            </a>
            <ul class="nav-links">
                <li><a href="#task">Tasks</a></li>
                <li><a href="#method">Method</a></li>
                <li><a href="#experiment">Experiment</a></li>
                <li><a href="#further-exploration">Further Exploration</a></li>
                <li><a href="#citation">Citation</a></li>
                <!-- <li><a href="#conclusion">Conclusion</a></li> -->
            </ul>
            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <button class="theme-toggle" aria-label="Toggle dark/light mode">
        <i class="fas fa-moon"></i>
        <span class="fallback-icon" style="display:none;">🌙</span>
    </button>
    <button class="back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
        <span class="fallback-icon" style="display:none;">⬆</span>
    </button>

    <!-- 封面设计 - 视频网格封面 -->
    <header class="cover-header">
        <!-- 添加Font Awesome图标库，确保图标可以正确加载 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
        <!-- 添加Animate.css库 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
        <!-- 添加自定义样式 -->
        <style>
            /* 封面样式 - 不受暗色模式影响 */
            .cover-header {
                position: relative !important;
                height: 100vh !important;
                width: 100% !important;
                overflow: hidden !important;
                margin-bottom: 0 !important;
                background-color: #000 !important;
                display: block !important;
            }

            /* 强制显示所有图标 */
            .fas, .fab, .ai {
                display: inline-block !important;
            }

            /* 隐藏所有fallback图标 */
            .fallback-icon {
                display: none !important;
            }

            /* 视频格子铺满全屏无间隙 */
            .video-grid {
                display: grid;
                grid-template-columns: repeat(4, 25vw);  /* 每列25% */
                grid-template-rows: repeat(3, 33.3333vh); /* 每行 1/3 */
                width: 100vw;
                height: 100vh;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 0;
                overflow: hidden;
                margin: 0;
                padding: 0;
                grid-gap: 0;
                background-color: #000;
            }

            .video-grid video {
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
                border: none;
                margin: 0;
                padding: 0;
                max-width: none;
                box-shadow: none;
                transition: none;
                transform: none;
                border-radius: 0;
                position: relative;
            }

            .video-grid video:hover {
                transform: none;
            }

            /* 灰色透明蒙版 - 不受暗色模式影响 */
            .overlay {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background-color: rgba(0, 0, 0, 0.5) !important;
                z-index: 1 !important;
            }

            /* 居中文字 */
            .text-container {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 2;
                text-align: center;
                color: white;
                text-shadow: 0 0 10px black;
                width: 90%;
                max-width: 1200px;
                font-family: 'Noto Serif SC', serif, 'SimHei', 'Microsoft YaHei', '黑体', '宋体';
            }

            .text-container h1 {
                font-size: 4.5em;
                margin-bottom: 0.3em;
                font-weight: 700;
                letter-spacing: 3px;
                color: white;
                line-height: 1.2;
                text-shadow: 0 0 15px rgba(0,0,0,0.7);
            }

            .text-container .main-subtitle {
                font-size: 1.8em;
                white-space: nowrap;
                margin: 0.2em 0;
                font-weight: 500;
                color: white;
                line-height: 1.5;
            }

            .text-container p {
                font-size: 1.2em;
                margin: 0.3em 0;
                font-weight: 400;
                color: rgba(255, 255, 255, 0.9);
                line-height: 1.5;
            }

            /* 特点容器样式 */
            .features-container {
                margin-top: 1rem;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                background-color: rgba(0, 0, 0, 0.4);
                border-radius: 10px;
                padding: 1.5rem;
                backdrop-filter: blur(5px);
                max-width: 700px;
                margin-left: auto;
                margin-right: auto;
            }

            .feature-item {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                text-align: left;
                font-size: 1em;
                margin: 0.25rem 0;
            }

            .feature-icon {
                color: #3182ce;
                font-size: 1.2em;
                flex-shrink: 0;
            }

            .feature-item span {
                font-weight: 600;
                color: #63b3ed;
            }

            /* 按钮样式 */
            .action-buttons {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 1rem;
                margin-top: 2rem;
                position: absolute;
                bottom: 5%;
                left: 0;
                right: 0;
                z-index: 3;
            }

            .action-button {
                padding: 0.8rem 1.5rem;
                border-radius: 50px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                transition: all 0.3s ease;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .action-button:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
                background: rgba(0, 0, 0, 0.8);
            }

            /* 主图样式 */
            .main-image-container {
                position: relative;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                height: 100%;
                display: flex;
                align-items: center;
                margin: 2rem auto;
            }

            .main-image-container:hover {
                transform: scale(1.01);
            }

            .main-image {
                width: 100%;
                height: auto;
                display: block;
            }

            /* 响应式调整 */
            @media (max-width: 768px) {
                .text-container h1 {
                    font-size: 2.5em;
                }

                .text-container .main-subtitle {
                    font-size: 1.3em;
                    white-space: normal;
                }

                .text-container p {
                    font-size: 1em;
                    white-space: normal;
                }

                .features-container {
                    padding: 1rem;
                    margin-top: 1.5rem;
                }

                .feature-item {
                    font-size: 0.9em;
                    gap: 0.5rem;
                }

                .action-button {
                    padding: 0.7rem 1.2rem;
                    font-size: 0.9rem;
                }
            }

            /* 导航栏样式 - 滚动时显示 */
            .navbar {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                transition: transform 0.3s ease, background-color 0.3s ease;
                transform: translateY(-100%);
            }

            .navbar.visible {
                transform: translateY(0);
            }
        </style>

        <!-- 视频网格 -->
        <div class="video-grid">
            <!-- 12 视频 -->
            <video src="source/videos/12.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/2.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/3.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/4.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/5.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/6.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/7.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/8.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/9.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/10.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/11.mp4" autoplay muted loop playsinline></video>
            <video src="source/videos/1.mp4" autoplay muted loop playsinline></video>
        </div>

        <!-- 灰色蒙版 -->
        <div class="overlay"></div>

        <!-- 中间白字 -->
        <div class="text-container text-center">
            <h1 class="text-center">Embodied-R</h1>
            <p class="text-center main-subtitle">Collaborative Framework for Activating Embodied Spatial</p>
            <p class="text-center main-subtitle">Reasoning in Foundation Models via Reinforcement Learning</p>

            <div class="features-container">
                <p class="feature-item text-center"><i class="fas fa-cogs feature-icon" style="color: #FFD700; font-weight: bold;"></i> <span style="color: #FFD700; font-weight: bold;">Collaborative Framework:</span> Combining large-scale VLMs for perception and small-scale LMs for reasoning</p>
                <p class="feature-item text-center"><i class="fas fa-award feature-icon" style="color: #FFD700; font-weight: bold;"></i> <span style="color: #FFD700; font-weight: bold;">Novel Reward System:</span> Think-answer logical consistency for enhanced slow-thinking capabilities</p>
                <p class="feature-item text-center"><i class="fas fa-chart-line feature-icon" style="color: #FFD700; font-weight: bold;"></i> <span style="color: #FFD700; font-weight: bold;">SOTA Performance:</span> Comparable to o1/Gemini-2.5-Pro in embodied spatial reasoning tasks</p>
                <p class="feature-item text-center"><i class="fas fa-brain feature-icon" style="color: #FFD700; font-weight: bold;"></i> <span style="color: #FFD700; font-weight: bold;">Emergent Thinking:</span> Exhibits systematic analysis and contextual integration patterns</p>
            </div>
        </div>

        <!-- 按钮区域 -->
        <div class="action-buttons">
            <a href="https://arxiv.org/abs/2504.12680" class="action-button">
                <i class="ai ai-arxiv"></i>
                <span>arXiv</span>
            </a>
            <a href="https://arxiv.org/pdf/2504.12680" class="action-button">
                <i class="fas fa-file-pdf"></i>
                <span>PDF</span>
            </a>
            <a href="https://github.com/EmbodiedCity/Embodied-R.code" class="action-button">
                <i class="fab fa-github"></i>
                <span>Code</span>
            </a>
        </div>
    </header>



    <div class="content-container" style="margin-top: 2rem; border-radius: 1rem;">

        <div class="authors-section fade-in my-6">
            <div class="flex flex-col items-center">
                <div class="w-full max-w-5xl mx-auto text-center">
                    <div class="text-xl mb-3 font-medium">
                        <p class="mb-2 flex justify-center flex-wrap gap-x-2">
                            <span class="author-name">Baining Zhao<sup>*</sup></span>,
                            <span class="author-name">Ziyou Wang<sup>*</sup></span>,
                            <span class="author-name">Jianjie Fang<sup>*</sup></span>,
                            <a href="mailto:<EMAIL>" class="author-link">Chen Gao<sup>†</sup></a>,
                            <span class="author-name">Fanghang Man</span>
                        </p>
                        <p class="flex justify-center flex-wrap gap-x-2">
                            <span class="author-name">Jinqiang Cui</span>,
                            <span class="author-name">Xin Wang</span>,
                            <a href="mailto:<EMAIL>" class="author-link">Xinlei Chen<sup>†</sup></a>,
                            <span class="author-name">Yong Li</span>,
                            <span class="author-name">Wenwu Zhu</span>
                        </p>
                    </div>

                    <div class="mb-4">
                        <div class="inline-block font-bold text-xl">
                            <i class="fas fa-university text-primary-color mr-1"></i>
                            <span>Tsinghua University</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h2 id="task" class="section-title text-center fade-in">Task of Embodied Reasoning</h2>
        <img src="source/task.png" alt="Task of Embodied Reasoning" class="fade-in" style="width: 85%; max-width: 1084px; height: auto; object-fit: contain;">

        <p class="fade-in">
            We focus on embodied spatial reasoning tasks, which require models to understand and reason about spatial relationships from sequential visual observations. We use two main datasets: VSI-Bench (indoor scenarios) and UrbanVideo-Bench (outdoor scenarios). These tasks present the following challenges:
        </p>
        <p class="fade-in">
            <strong>1. Integration of Perception and Reasoning</strong>: Reasoning is always built upon perception. For the studied problem, continuous visual observations impose higher demands on perception. Reasoning cannot be well achieved with faulty perceptions or hallucinations. It is challenging to reason when it is already hard to perceive from the videos.
        </p>
        <p class="fade-in">
            <strong>2. Complex Spatio-temporal Relationships</strong>: Video data naturally involves complex spatio-temporal relationships, requiring the discovery of object associations across frames and the extraction of semantics relevant to the reasoning task. For instance, to navigate to a destination outside the current field of view, one must infer their location from historical visual observations, build a mental map of the environment, develop a high-level plan to determine the direction, and finally decide on specific actions to execute.
        </p>
        <p class="fade-in">
            <strong>3. Distinct Characteristics of Embodied Observations</strong>: First, egocentric videos focus on understanding the relationship between the observer and the surrounding environment, often from a constrained first-person perspective. Second, embodied continuous visual observations are generated over time, indicating that embodied perception should rely on sequential inputs rather than aggregating all visual observations for a single input after a prolonged period. Finally, embodied visual observations also exhibit spatial continuity, meaning there is significant redundancy and repetition between frames.
        </p>

        <!-- 整合视频和问题的容器 -->
        <div class="video-container mt-8">
            <!-- 室外任务框 -->
            <div class="video-wrapper task-box">
                <h3 class="text-center text-xl font-semibold mb-3">Outdoor</h3>
                <div class="video-question-container">
                    <!-- 视频部分 -->
                    <div class="video-aspect-wrapper">
                        <video controls class="shadow-lg">
                            <source src="source/videos/13.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    <!-- 问题部分 -->
                    <div class="question-section">
                        <p>
                            <strong>Question:</strong> Navigation Instruction given at initial position: [Observe around the square area, then fly towards the highway, then turn left and land on the roof of the building on the left]. You move according to a series of movement instructions. What are you doing now?
                        </p>
                        <ul class="mb-4">
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> A. I look around the square area.</li>
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> B. I turn left and land on the roof of the building on the left.</li>
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> C. I fly towards the road.</li>
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> D. I fly over the park.</li>
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> E. I land.</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 室内任务框 -->
            <div class="video-wrapper task-box">
                <h3 class="text-center text-xl font-semibold mb-3">Indoor</h3>
                <div class="video-question-container">
                    <!-- 视频部分 -->
                    <div class="video-aspect-wrapper">
                        <video controls class="shadow-lg">
                            <source src="source/videos/14.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    <!-- 问题部分 -->
                    <div class="question-section">
                        <p>
                            <strong>Question:</strong> What will be the first-time appearance order of the following categories in the video: table, backpack, trash bin, lamp?
                        </p>
                        <ul class="mb-4">
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> A. table, backpack, trash bin, lamp</li>
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> B. backpack, lamp, trash bin, table</li>
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> C. lamp, table, trash bin, backpack</li>
                            <li><i class="fas fa-circle mr-2 text-black-500 text-xs"></i> D. backpack, table, trash bin, lamp</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <h2 id="method" class="section-title text-center fade-in">Method</h2>
        <div class="method-section">
            <p class="fade-in">
                Our proposed Embodied-R is a collaborative framework combining large-scale Vision-Language Models (VLMs) for perception and small-scale Language Models (LMs) for reasoning. By decoupling perception and reasoning, we can leverage the perceptual capabilities of large-scale VLMs while training resource-efficient small-scale LMs to activate embodied reasoning through reinforcement learning.
            </p>
            <img src="source/3.jpg" alt="Method Overview" class="fade-in" style="width: 85%; max-width: 1084px; height: auto; object-fit: contain;">
            <h3 class="subsection-title fade-in">Large-Scale VLM-based Perception</h3>
            <p class="fade-in">
                <strong>Key-Frame Extractor</strong>: As the agent moves continuously in space, high sampling frequencies result in significant overlap between consecutive frames. On one hand, the VLM relies on changes in static objects within the environment across frames to infer the agent's pose variation. On the other hand, excessive overlap between frames leads to increased inference costs for both the VLM and LLM. To address this, we designed a key-frame extractor tailored to the characteristics of embodied videos, selecting key frames that retain overlap while ensuring sufficient information gain between them.
            </p>
            <p class="fade-in">
                <strong>Embodied Semantic Representation</strong>: Since perceptual capability is positively correlated with model size, we employ a large-scale VLM to process visual inputs to ensure high-quality perception. The differential information of each key frame is described sequentially. This approach provides two key benefits: 1) The sequential and dynamic processing aligns better with the characteristics of embodied scenarios, where visual observations are continuously generated over time. 2) It facilitates the handling of long videos by avoiding the input token limitations that arise when all frames are processed simultaneously by the VLM.
            </p>
            <h3 class="subsection-title fade-in">Small-Scale LM-based Reasoning</h3>
            <p class="fade-in">
                Given semantic perception, we can inference the answer through a LM, which can be trained with limit computational resource to boost the spatial reasoning ability.
            </p>
            <p class="fade-in">
                <strong>Group Relative Policy Optimization (GRPO)</strong>: We adopt a computationally efficient RL training strategy, GRPO. For a given query and semantic annotation, GRPO generates a group of outputs using the reference policy, then updates the policy model by optimizing the objective function.
            </p>
            <p class="fade-in">
                <strong>Reward Modeling</strong>: We propose three types of rewards: format reward, accuracy reward, and logical consistency reward. These are designed to respectively guide the model to learn the "think-answer" reasoning pattern, accurate embodied spatial reasoning, and logical consistency between reasoning and the answer. Notably, we introduce a novel logical consistency reward to address reward hacking behaviors observed during training, ensuring alignment between the reasoning process and the final answer.
            </p>
            <p class="fade-in">
                <strong>Three-Stage Training Schedule</strong>: We design a three-stage training schedule to achieve a smooth improvement in training performance. The primary distinction between stages lies in the different weight ratios assigned to the three types of rewards: Stage 1 (epochs 1-2) focuses on format rewards; Stage 2 (epochs 3-4) shifts to improving the accuracy of model responses; Stage 3 (epochs 5-12) aims to enhance accuracy while simultaneously improving the quality of the "thinking" process.
            </p>
        </div>


        <h2 id="experiment" class="section-title text-center fade-in">Experiment</h2>
        <div class="experiment-section">
            <h3 class="subsection-title fade-in">Experimental Setup</h3>
            <p class="fade-in">
                We primarily focus on spatial reasoning problems during motion within three-dimensional physical space. We selected two embodied video datasets as the main training and testing sets: VSI-Bench (indoor first-person navigation data) and UrbanVideo-Bench (outdoor embodied data captured by drones navigating through aerial spaces). These datasets provide diversity in scenarios by incorporating both indoor and outdoor video data. We specifically selected four distinct types of tasks from each dataset, characterized by long spatial reasoning chains and low accuracy.
            </p>
            <p class="fade-in">
                In our experiments, the base models adopted by Embodied-R are: VLM: Qwen2.5-VL-72B-Instruct, LLM: Qwen2.5-3B-Instruct. We conducted five repeated experiments, with the dataset randomly divided into five equal parts and 5-fold cross-validation adopted. The final testing results are averaged across the five experiments.
            </p>

            <h3 class="subsection-title fade-in">RQ1:How does Embodied-R perform compared to existing video-LLMs?</h3>
            <p class="fade-in">
                Experimental results demonstrate that our proposed reasoning-enhanced model outperforms proprietary models by over 10%+ and SFT-trained models by 5%+.
            </p>

            <img src="source/Overall_Performance.png" alt="Overall Performance" class="fade-in" style="width: 85%; max-width: 1084px; height: auto; object-fit: contain;">

            <h3 class="subsection-title fade-in">RQ2:Has Embodied-R Learned Slow-Thinking?</h3>
            <p class="fade-in">
                Beyond the quantitative results, we aim to explore whether spatial reasoning capabilities in the output of Embodied-R are improved. After RL training, Embodied-R demonstrates the following human-like reasoning ways:
            </p>
            <p class="fade-in">
                <strong>1. Spatial Relationship Reasoning</strong>: Accurately inferring the relative spatial relationship between itself and the surrounding environment.<br>
                <strong>2. Systematic Analysis</strong>: Breaking down problems into components, presenting answers with a "part-to-whole" structure, and maintaining clear logical organization.<br>
                <strong>3. Contextual Integration</strong>: Integrating semantic information across different frames to perform comprehensive analysis.<br>
                <strong>4. Think-Answer Format</strong>: Strictly adhering to a structured process of reasoning before outputting the final answer.
            </p>
            <img src="source/case.jpg" alt="case" class="fade-in" style="width: 85%; max-width: 1084px; height: auto; object-fit: contain;">

            <h3 class="subsection-title fade-in">RQ3:What are the contributions of each module?</h3>
            <p class="fade-in">
                <strong>Ablation of Key-Frame Extractor</strong>: The role of Key-Frame Extractor is to reduce inference time and training time by retaining essential frames and removing redundant ones while maintaining perceptual quality. With negligible differences in accuracy, training time is significantly reduced by 8.7%, and single inference time is reduced by approximately one-third.
            </p>
            <p class="fade-in">
                <strong>Ablation of Collaboration</strong>: The collaborative framework enables improved reasoning capabilities under limited computational resources for training. With training-free large-scale pretrained VLMs, it only requires training small-scale LM models to achieve enhanced reasoning performance. With identical key-frame inputs and using the same VLM, Qwen2.5-VL-72B-Instruct, the overall accuracy of collaborative inference is 1.5 times higher than that of the standalone VLM.
            </p>
            <p class="fade-in">
                <strong>Ablation of RL Training</strong>: RL is central to the LM training in this paper. Without RL training, directly applying the original LM-3B model for reasoning leads to poor performance, as the LM has limited exposure to embodied spatial reasoning data during pretraining. After RL training, the LM achieves significant improvements, with a 27.9% increase on the UrbanVideo-Bench and a 20.6% increase on the VSI-Bench benchmarks.
            </p>


        </div>

        <h2 id="further-exploration" class="section-title text-center fade-in">Further Exploration</h2>
        <img src="source/4.png" alt="Relationship between Inference Ability, Aha Moments, and Response Length" class="fade-in" style="width: 85%; max-width: 1084px; height: auto; object-fit: contain;">

        <div class="further-exploration-section">
            <h3 class="subsection-title fade-in">RQ4:What is the Relationship Between Inference Ability, Aha Moments, and Response Length?</h3>
            <p class="fade-in">
                The GRPO training process involves tracking the validation set's accuracy reward, format reward, ratio of logical consistency reward to accuracy reward, and the response length. Notably, existing pure-text-based reproductions of DeepSeek-R-Zero models identify inference length and the "aha moment" as key indicators of emergent reasoning capabilities. However, such phenomena are rarely observed in other multimodal reasoning tasks, such as image-based reasoning.
            </p>
            <p class="fade-in">
                This leads us to hypothesize that response length is strongly influenced by the nature of the question itself. For instance, mathematical problems often require multi-step calculations, where increased reasoning length tends to correlate positively with reasoning ability. In contrast, for multimodal reasoning tasks like embodied spatial reasoning, the LM model training process converges toward an optimal range of text output distributions. Concise reasoning patterns may facilitate embodied spatial reasoning. This highlights the versatility of RL-based post-training method, demonstrating the ability to benefit a wide range of reasoning tasks.
            </p>

            <h3 class="subsection-title fade-in">RQ5:Why Not Directly Perform RL on VLLMs?</h3>
            <p class="fade-in">
                We previously attempted direct RL training on the Qwen-VL-3B-Instruct model. Under similar training parameters and time, the performance of the VLM was notably inferior to that of the LM. Upon convergence, the VLM achieved an accuracy of 43.8% on the test set, significantly lower than the LM. The limited perceptual capability of the VLM restricts its potential for reasoning improvements. Therefore, under resource-constrained conditions, collaborative inference integrating models of different scales present a promising solution.
            </p>

            <h3 class="subsection-title fade-in">RQ6:Is Accuracy+Format Rewards All You Need?</h3>
            <p class="fade-in">
                According to the Deepseek-R1-Zero, it appears that accuracy and format rewards are enough to guide the model toward correct reasoning. However, during training in our problem, we observed instances of reward hacking, where the model optimizes the answer but the reasoning process leading to that answer is inconsistent with the answer itself. We aim to ensure alignment between the model's reasoning process and its answer, both to enhance generalization and improve the interpretability of the reasoning process.
            </p>
            <p class="fade-in">
                We employ GPT-4o to evaluate the proportion of logically consistent outputs on the test set before and after incorporating a logical consistency reward. This proportion increased from 46.01% to 99.43% after the reward was added, demonstrating the value of this approach in addressing embodied spatial multiple-choice reasoning tasks. Moreover, this reward mechanism could potentially be extended to other reasoning tasks prone to answer accuracy hacking during training.
            </p>

            <h3 class="subsection-title fade-in">RQ7:RL vs SFT when Generalize to Out-of-Distribution (OOD) Embodied Tasks</h3>
            <p class="fade-in">
                For small-scale LMs, we aim to explore their generalization performance when trained with SFT instead of RL. To evaluate this, we introduced two OOD datasets: EgoSchema and the egocentric task in MVBench. These two OOD datasets differ significantly from the training set in both task content and scene characteristics.
            </p>
            <p class="fade-in">
                RL-trained models demonstrate generalization ability across both datasets. On the EgoSchema dataset, the RL-trained language model under the Embodied-R framework even achieves performance comparable to the state-of-the-art multimodal reasoning model, Gemini-2.5-Pro. SFT-trained models showed improvement on EgoSchema but a decline on MVBench. This suggests that slow reasoning, as employed in RL models, could be a promising approach to improve the generalization capabilities even for small-scale models.
            </p>
        </div>

        <!-- <h2 id="conclusion" class="section-title text-center fade-in">Conclusion</h2>
        <p class="fade-in">
            To address embodied spatial reasoning tasks, we propose a collaborative framework that leverages the perceptual capabilities of large-scale VLMs and the reasoning potential of compact LMs. Through 90 hours of RL training on a 3B LM using 8 NVIDIA A800-SXM4-40GB GPUs, Embodied-R surpasses OpenAI-o1 by 13.9% and Gemini-2.5-Pro by 10.3% on the test set.
        </p>
        <p class="fade-in">
            Other key findings include: (1) RL training leads to output length convergence, aligning with the requirements of the task; (2) the reasoning upper bound of same-scale VLMs trained with RL is significantly lower than that of Embodied-R, due to inherent limitations in perception; (3) the proposed logical consistency reward enhances reasoning quality; and (4) models trained via RL exhibit stronger generalization on out-of-distribution datasets compared to those trained with SFT.
        </p> -->


    <script>
        // Theme toggle functionality
        const themeToggle = document.querySelector('.theme-toggle');
        const htmlElement = document.documentElement;
        const themeIcon = document.querySelector('.theme-toggle i');
        const themeIconFallback = document.querySelector('.theme-toggle .fallback-icon');

        // 检查Font Awesome是否加载成功
        function checkFontAwesome() {
            const spanFA = document.createElement('span');
            spanFA.className = 'fa';
            spanFA.style.display = 'none';
            document.body.insertBefore(spanFA, document.body.firstChild);

            const isFALoaded = window.getComputedStyle(spanFA).fontFamily.includes('FontAwesome');
            document.body.removeChild(spanFA);

            if (!isFALoaded) {
                // 如果Font Awesome未加载，显示备用图标
                document.querySelectorAll('.fallback-icon').forEach(el => {
                    el.style.display = 'inline';
                });
                document.querySelectorAll('.fas').forEach(el => {
                    el.style.display = 'none';
                });

                // 替换列表项的圆点图标
                document.querySelectorAll('ul li .fas.fa-circle').forEach(el => {
                    el.parentNode.innerHTML = '● ' + el.parentNode.innerHTML.replace(el.outerHTML, '');
                });
            }
        }

        // 页面加载后检查Font Awesome
        window.addEventListener('load', checkFontAwesome);

        // Check for system preference or saved preference
        if (localStorage.getItem('theme') === 'dark' ||
            (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
            themeIconFallback.textContent = '☀️';
        }

        themeToggle.addEventListener('click', () => {
            htmlElement.classList.toggle('dark');

            if (htmlElement.classList.contains('dark')) {
                localStorage.setItem('theme', 'dark');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                themeIconFallback.textContent = '☀️';
            } else {
                localStorage.setItem('theme', 'light');
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                themeIconFallback.textContent = '🌙';
            }
        });

        // Back to top functionality
        const backToTopButton = document.querySelector('.back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }

            // 更新阅读进度条
            updateReadingProgress();
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 移动端菜单切换
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuBtn.addEventListener('click', () => {
            navLinks.classList.toggle('active');
        });

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });

                    // 如果在移动端，点击后关闭菜单
                    if (window.innerWidth <= 768) {
                        navLinks.classList.remove('active');
                    }
                }
            });
        });

        // 滚动动效
        function checkVisibility() {
            const fadeElements = document.querySelectorAll('.fade-in');
            const staggerItems = document.querySelectorAll('.stagger-item');

            fadeElements.forEach((element, index) => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('visible');
                }
            });

            staggerItems.forEach((element, index) => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < window.innerHeight - elementVisible) {
                    setTimeout(() => {
                        element.classList.add('visible');
                    }, 100 * (index % 5)); // 每5个元素一组，错开显示时间
                }
            });
        }

        // 初始检查可见性
        window.addEventListener('load', checkVisibility);
        // 滚动时检查可见性
        window.addEventListener('scroll', checkVisibility);

        // 确保所有text-center类的元素都居中显示
        function enforceTextCenter() {
            document.querySelectorAll('.text-center').forEach(el => {
                el.style.textAlign = 'center';
            });

            document.querySelectorAll('.main-title, .section-title, h3.text-center').forEach(el => {
                el.style.textAlign = 'center';
            });
        }

        // 初始检查可见性和样式
        window.addEventListener('load', function() {
            checkVisibility();
            checkFontAwesome();
            enforceTextCenter();
            updateReadingProgress();
        });

        // 阅读进度条功能
        function updateReadingProgress() {
            const progressBar = document.getElementById('reading-progress');
            const totalHeight = document.body.scrollHeight - window.innerHeight;
            const progress = (window.scrollY / totalHeight) * 100;
            progressBar.style.width = `${progress}%`;

            // 控制导航栏显示
            const navbar = document.querySelector('.navbar');
            const coverHeader = document.querySelector('.cover-header');
            const coverHeight = coverHeader.offsetHeight;

            if (window.scrollY > coverHeight - 100) {
                navbar.classList.add('visible');
            } else {
                navbar.classList.remove('visible');
            }
        }

        // 监听滚动事件以更新进度条和导航栏显示
        window.addEventListener('scroll', updateReadingProgress);
    </script>

    <!-- 引用部分 -->
    <div class="content-container" style="margin-top: 2rem; border-radius: 1rem;">
        <h2 id="citation" class="section-title text-center fade-in">Citation</h2>
        <div class="citation-section">
            <pre style="background-color: var(--bg-color); padding: 1.5rem; border-radius: 0.5rem; overflow-x: auto; font-size: 0.9rem; border: 1px solid var(--border-color); box-shadow: var(--shadow);">
@misc{zhao2025embodiedr,
      title={Embodied-R: Collaborative Framework for Activating Embodied Spatial Reasoning in Foundation Models via Reinforcement Learning},
      author={Baining Zhao and Ziyou Wang and Jianjie Fang and Chen Gao and Fanhang Man and Jinqiang Cui and Xin Wang and Xinlei Chen and Yong Li and Wenwu Zhu},
      year={2025},
      eprint={2504.12680},
      archivePrefix={arXiv},
      primaryClass={cs.AI},
      url={https://arxiv.org/abs/2504.12680},
}
            </pre>
        </div>
    </div>
</body>
</html>
